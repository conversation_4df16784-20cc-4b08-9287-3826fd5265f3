using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.DTOs;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class RemainingDebtManager : IRemainingDebtService
    {
        private readonly IRemainingDebtDal _remainingDebtDal;
        private readonly ICompanyContext _companyContext;

        public RemainingDebtManager(IRemainingDebtDal remainingDebtDal, ICompanyContext companyContext)
        {
            _remainingDebtDal = remainingDebtDal;
            _companyContext = companyContext;
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(duration: 600)] // 10 dakika - Dashboard verisi
        public IDataResult<List<RemainingDebtDetailDto>> GetRemainingDebtDetails()
        {
            return new SuccessDataResult<List<RemainingDebtDetailDto>>(_remainingDebtDal.GetRemainingDebtDetails());
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [CacheInvalidationAspect("RemainingDebt", "Payment")]
        public IResult AddDebtPayment(DebtPaymentDto debtPaymentDto)
        {
            // SOLID prensiplerine uygun: Karmaşık business logic DAL katmanına devredildi
            return _remainingDebtDal.AddDebtPaymentWithBusinessLogic(debtPaymentDto);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [CacheInvalidationAspect("RemainingDebt")]
        public IResult Delete(int remainingDebtId)
        {
            // SOLID prensiplerine uygun: Soft delete işlemini DAL katmanına devredildi
            return _remainingDebtDal.SoftDeleteRemainingDebt(remainingDebtId, _companyContext.GetCompanyId());
        }
    }
}